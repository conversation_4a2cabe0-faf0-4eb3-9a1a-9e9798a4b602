{"name": "hennesy", "version": "1.0.0", "main": "index.js", "scripts": {"deploy:local": "sh scripts/prepackage.sh && serverless deploy --stage local", "deploy:snd": "sh scripts/prepackage.sh && python3.11 scripts_python/deploy.py snd-hen snd-hen_qa", "deploy:snd:function": "sh scripts/prepackage.sh && python3.11 scripts_python/deploy.py snd-hen snd-hen_qa --function=$npm_config_function", "deploy:snd:stepfunction": "sh scripts/prepackage.sh && python3.11 scripts_python/deploy.py snd-hen snd-hen_qa --stepfunction=$npm_config_function", "deploy:prd": "sh scripts/prepackage.sh && python3.11 scripts_python/deploy.py prd-hen hen_prd", "deploy:prd:function": "sh scripts/prepackage.sh && python3.11 scripts_python/deploy.py prd-hen hen_prd --function=$npm_config_function"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"serverless-deployment-bucket": "^1.6.0", "serverless-hooks": "^1.0.0", "serverless-localstack": "^1.3.1", "serverless-offline": "^14.3.4", "serverless-plugin-common-excludes": "^4.0.0", "serverless-plugin-include-dependencies": "^6.1.1", "serverless-step-functions": "^3.21.1"}, "dependencies": {"serverless": "^4.7.0"}, "overrides": {"serverless-deployment-bucket": {"serverless": "$serverless"}}}